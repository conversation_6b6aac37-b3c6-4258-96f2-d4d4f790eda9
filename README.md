# HTML Previewer Pro

A powerful, enhanced HTML development environment with live preview, file management, and persistent storage capabilities.

## 🚀 Features

### ✨ Core Functionality
- **Live HTML Preview** - Real-time preview as you type
- **Syntax Highlighting** - Beautiful code highlighting with Prism.js
- **Persistent Storage** - Remembers your project state across sessions
- **Virtual File System** - Create and manage files without touching disk
- **Multi-file Support** - Work with multiple HTML files simultaneously
- **HTML Code Formatting** - Format and beautify HTML code with Ctrl+Shift+F
- **HTML Validation** - Real-time validation with error highlighting
- **Auto-complete** - Smart HTML tag completion and suggestions

### 📁 File Management
- **Project Explorer** - Browse and manage your HTML files with file type icons
- **New File Creation** - Quick file creation with templates
- **New Folder Creation** - Organize files in folders
- **File Import** - Drag & drop or import existing HTML files
- **File Rename** - Rename files and folders with validation
- **File Deletion** - Delete files/folders with confirmation modal
- **ZIP Export** - Save all modified files as a ZIP archive
- **Context Menu** - Right-click files for additional options

### 🎛️ Panel Management
- **Collapsible Panels** - Toggle file explorer, editor, and preview
- **Draggable Resizing** - Resize panels by dragging borders
- **Persistent Panel States** - Remembers panel visibility and sizes
- **Improved Toggle System** - Toggle buttons remain accessible when collapsed

### ⌨️ Keyboard Shortcuts
- `Ctrl+N` - Create new file
- `Ctrl+S` - Save current file
- `Ctrl+Shift+S` - Save all modified files
- `Ctrl+F` - Search in current file
- `Ctrl+Shift+H` - Search and replace across files
- `Ctrl+Shift+F` - Format HTML code
- `Ctrl+Z` - Undo
- `Ctrl+Y` - Redo
- `Escape` - Close modals

### 🎨 User Experience
- **Dark/Light Mode** - Toggle between themes
- **File Status Indicators** - Visual indicators for saved/modified files
- **File Type Icons** - Distinctive icons for HTML, CSS, JS, and other file types
- **Line Numbers** - Toggle line numbers display
- **Undo/Redo** - Full history system with 50-entry limit
- **Toast Notifications** - User-friendly feedback messages
- **Console Integration** - View JavaScript errors and logs
- **Drag & Drop Support** - Import files by dragging

## 🛠️ Technical Implementation

### Project Structure
```
HTML_Previewer_Pro/
├── HTML_Previewer.html    # Main application entry point
├── styles/
│   └── app.css           # All CSS styles consolidated
├── scripts/
│   └── app.js            # All JavaScript functionality
├── README.md             # This documentation
├── TODO.md               # Development roadmap
└── tasks_completed.md    # Completed features log
```

### Asset Loading Order
The application loads external dependencies in this specific order:
1. **Tailwind CSS** - Utility-first CSS framework (CDN)
2. **Prism.js CSS** - Syntax highlighting styles (CDN)
3. **JSZip** - ZIP file creation library (CDN)
4. **Prism.js** - Syntax highlighting engine (CDN)
5. **app.css** - Custom application styles
6. **app.js** - Main application logic (loaded with defer)

### Architecture
- **Modular Design** - Separate classes for different functionality
- **State Management** - Centralized application state
- **Local Storage** - Persistent data storage
- **Virtual File System** - In-memory file management
- **Component-based** - Organized into logical controllers

### Key Components
- `FileManager` - Handles file operations and project management
- `EditorController` - Manages the code editor functionality
- `PreviewEngine` - Handles live preview and console capture
- `PanelManager` - Manages panel visibility and states
- `PersistenceManager` - Handles data persistence
- `SearchManager` - Handles search and replace functionality

### Storage Structure
```javascript
// localStorage keys used:
- htmlPreviewerDarkMode: boolean
- htmlPreviewerLastFolder: string
- htmlPreviewerPanelStates: object
- htmlPreviewerSession: object (complete session data)
- htmlPreviewerPanelSizes: object (panel width/height settings)
- htmlPreviewerLineNumbers: boolean (line numbers preference)
```

## 📋 Usage

### Getting Started
1. Open `HTML_Previewer.html` in your web browser
2. Create a new file or select a project folder
3. Start coding with live preview
4. Your work is automatically saved to localStorage

### Creating New Files
1. Click the "+" button in the Project Explorer
2. Enter a filename (`.html` extension will be added automatically)
3. Start coding with the provided template

### Importing Files
1. Use the import button or drag & drop HTML files
2. Files are imported into the virtual file system
3. Save to download files to your computer

### Project Management
1. Select a folder to load existing HTML projects
2. Use the file tree to navigate between files
3. Right-click files for context menu options

## 🔧 Browser Requirements

- Modern browser with ES6+ support
- localStorage support required for persistence
- File API support for folder/file operations

## 🎯 Best Practices

1. **Regular Saving** - Use `Ctrl+S` to save individual files
2. **Project Organization** - Use folders to organize related files
3. **Auto-save** - The application auto-saves session data every 30 seconds
4. **Valid HTML** - The editor validates basic HTML structure before saving

## 🔧 Development & Customization

### Adding Custom Styles
To add custom CSS styles to the application:
1. Open `styles/app.css`
2. Add your custom CSS at the end of the file
3. Use existing CSS variables for consistency:
   ```css
   /* Example custom styles */
   .my-custom-class {
     background-color: var(--bg-primary);
     color: var(--text-primary);
     border: 1px solid var(--border-color);
   }
   ```

### Adding Custom JavaScript
To extend the application functionality:
1. Open `scripts/app.js`
2. Add your custom functions at the end of the file
3. Follow the existing patterns:
   ```javascript
   // Example custom functionality
   function myCustomFeature() {
     // Your code here
     showToast('Custom feature executed!', 'success');
   }
   ```

### Contributing Guidelines
When contributing to the codebase:

#### CSS Guidelines
- Use existing CSS variables for colors and spacing
- Follow the established naming conventions
- Add comments for complex styles
- Test in both light and dark modes
- Ensure responsive design compatibility

#### JavaScript Guidelines
- Use ES6+ features consistently
- Follow the existing class-based architecture
- Add error handling with try-catch blocks
- Use the existing toast system for user feedback
- Update localStorage keys documentation when adding new persistence

#### File Organization
- Keep all CSS in `styles/app.css`
- Keep all JavaScript in `scripts/app.js`
- Don't create additional CSS/JS files unless absolutely necessary
- Update this README when adding new features

## 🚧 Current Limitations

- **Virtual files** exist only in browser storage until downloaded
- **File size limits** based on browser localStorage capacity
- **Mobile responsiveness** needs improvement for smaller screens
- **Syntax highlighting** currently limited to HTML (CSS/JS planned)

## 💡 Tips

- Use the console toggle to debug JavaScript in your HTML
- Panel states are remembered between sessions
- Files show visual indicators when modified
- Dark mode preference is persistent
- The application works offline once loaded

## 🐛 Troubleshooting

### Common Issues
1. **Files not loading** - Check browser console for errors
2. **Storage full** - Clear localStorage or export/save important files
3. **Panel stuck** - Refresh page to reset panel states
4. **Preview not updating** - Use the refresh preview button

### Browser Support
- Chrome/Edge: Full support
- Firefox: Full support  
- Safari: Full support
- IE: Not supported

---

**HTML Previewer Pro** - Your enhanced HTML development environment 